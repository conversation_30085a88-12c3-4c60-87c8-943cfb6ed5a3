'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { AnimatedText } from '@/components/ui/animated-text';
import { TextReveal } from '@/components/ui/aceternity/text-reveal';
import { <PERSON><PERSON>hBackground, GlassCard } from '@/components/ui/aceternity/bokeh-background';
import { Spotlight } from '@/components/ui/aceternity/spotlight';
import { RocketIcon, LightningBoltIcon, StarIcon, MixerHorizontalIcon, GlobeIcon } from '@radix-ui/react-icons';
import TextHoverEffectDemo from '@/components/ui/text-hover-effect-demo';

export default function ProgramsClientPage() {
  return (
    <>
      {/* Background Text Effect - Footer Area Only */}
      <div className="fixed bottom-0 left-0 right-0 h-[600px] z-[5] flex items-center justify-center pointer-events-none">
        <div className="pointer-events-auto relative z-[10]">
          <TextHoverEffectDemo />
        </div>
      </div>

      {/* Hero Section with New Background */}
      <section className="relative h-[70vh] overflow-hidden">
        {/* Background Image with Overlay */}
        <div className="absolute inset-0 bg-black">
          <div
            className="absolute inset-0 bg-cover bg-center opacity-70"
            style={{
              backgroundImage: 'url(/images/programs/1.jpg)',
              backgroundPosition: 'center center'
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black" />
        </div>

        {/* Content */}
        <div className="container mx-auto px-4 h-full flex flex-col justify-center relative z-10">
          <div className="max-w-4xl">
            <AnimatedText
              text="ИННОВАЦИЙН ХӨТӨЛБӨРҮҮД"
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6"
              once
            />
            <TextReveal>
              <p className="text-xl text-white/80 max-w-2xl">
                Бизнес санаагаа хөгжүүлэх, өсөлтийг хурдасгах, амжилтанд хүрэхэд тань туслах хөтөлбөрүүд
              </p>
            </TextReveal>
          </div>
        </div>
      </section>

      {/* Programs Section with Bokeh Background */}
      <BokehBackground
        className="py-24"
        colors={['#8b5cf6', '#a855f7', '#c084fc', '#e879f9', '#fbbf24', '#f59e0b']}
        density={60}
        speed={1.5}
      >
        <div className="container mx-auto px-4">
          <TextReveal className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-4xl font-bold mb-4 text-white">
              ХӨТӨЛБӨРҮҮД
            </h2>
            <p className="text-white/70">
              Бизнес санаагаа хөгжүүлэх, өсөлтийг хурдасгах, амжилтанд хүрэхэд тань туслах хөтөлбөрүүд
            </p>
          </TextReveal>

          {/* Program Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
            <ProgramCard
              title="ГАРААНЫ ХӨТӨЛБӨР"
              description="Шинэ бизнес санаагаа эхлүүлэх, хөгжүүлэх, зах зээлд гаргахад шаардлагатай мэдлэг, ур чадвар, холбоо сүлжээг олгох иж бүрэн хөтөлбөр."
              icon={<RocketIcon className="w-8 h-8" />}
              delay={0}
              glowColor="rgba(255, 170, 0, 0.3)"
              buttonText="Дэлгэрэнгүй"
            />

            <ProgramCard
              title="ӨСӨЛТИЙН ХӨТӨЛБӨР"
              description="Бизнесээ өргөжүүлэх, зах зээлийн хувиа нэмэгдүүлэх, борлуулалтаа өсгөх, санхүүжилт татах зэрэг өсөлтийн шатны сорилтуудыг даван туулахад туслах хөтөлбөр."
              icon={<LightningBoltIcon className="w-8 h-8" />}
              delay={0.1}
              glowColor="rgba(100, 200, 255, 0.3)"
              buttonText="Дэлгэрэнгүй"
            />
          </div>

          {/* Small Program Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <ProgramCard
              title="МЭРГЭЖИЛТНИЙ ХӨТӨЛБӨР"
              description="Тодорхой чиглэлээр мэргэшсэн мэргэжилтнүүдийг бэлтгэх, ур чадварыг нь дээшлүүлэх, салбарын шилдэг туршлагуудыг судлах боломжийг олгох хөтөлбөр."
              icon={<StarIcon className="w-8 h-8" />}
              delay={0.2}
              glowColor="rgba(255, 100, 100, 0.3)"
              buttonText="Дэлгэрэнгүй"
              small
            />

            <ProgramCard
              title="ТЕХНОЛОГИЙН ХӨТӨЛБӨР"
              description="Шинэ технологи, инновацийг бизнестээ нэвтрүүлэх, дижитал шилжилт хийх, технологийн чадавхаа бэхжүүлэхэд туслах хөтөлбөр."
              icon={<MixerHorizontalIcon className="w-8 h-8" />}
              delay={0.3}
              glowColor="rgba(100, 255, 100, 0.3)"
              buttonText="Дэлгэрэнгүй"
              small
            />

            <ProgramCard
              title="ГЛОБАЛ ХӨТӨЛБӨР"
              description="Бизнесээ олон улсын зах зээлд гаргах, гадаад харилцаа холбоо тогтоох, экспортын боломжуудыг судлах, олон улсын стандартад нийцүүлэхэд туслах хөтөлбөр."
              icon={<GlobeIcon className="w-8 h-8" />}
              delay={0.4}
              glowColor="rgba(200, 100, 255, 0.3)"
              buttonText="Дэлгэрэнгүй"
              small
            />
          </div>
        </div>
      </BokehBackground>

      {/* Featured Program */}
      <section className="py-20 bg-black relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-primary/5 to-transparent opacity-50" />

        {/* Floating particles */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute rounded-full bg-primary/20"
              style={{
                width: Math.random() * 10 + 5,
                height: Math.random() * 10 + 5,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -30, 0],
                opacity: [0.2, 0.5, 0.2],
              }}
              transition={{
                duration: Math.random() * 5 + 5,
                repeat: Infinity,
                delay: Math.random() * 5,
              }}
            />
          ))}
        </div>

        <div className="container mx-auto px-4 relative z-10 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Хамтдаа амжилтанд хүрцгээе
          </h2>
          <p className="text-xl text-white/70 max-w-2xl mx-auto mb-8">
            Бизнес санаагаа амжилттай хэрэгжүүлэхэд туслах хөтөлбөрт өнөөдөр бүртгүүлээрэй
          </p>
          <button className="px-8 py-3 bg-white text-primary rounded-full font-medium hover:bg-white/90 transition-colors duration-300 shadow-lg">
            Холбогдох
          </button>
        </div>
      </section>
    </>
  );
}

// Enhanced Program Card Component
function ProgramCard({
  title,
  description,
  icon,
  delay = 0,
  glowColor = 'rgba(var(--primary), 0.2)',
  buttonText = 'Дэлгэрэнгүй',
  small = false
}: {
  title: string;
  description: string;
  icon?: React.ReactNode;
  delay?: number;
  glowColor?: string;
  buttonText?: string;
  small?: boolean;
}) {
  return (
    <motion.div
      className={`group relative overflow-hidden rounded-xl border border-white/10 bg-black/40 backdrop-blur-md p-8 transition-all duration-500 hover:border-primary/30 hover:bg-black/50 ${small ? 'h-full' : ''}`}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      viewport={{ once: true }}
      whileHover={{ y: -8, scale: 1.02 }}
    >
      {/* Animated background glow effect */}
      <motion.div
        className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
        style={{
          background: `radial-gradient(circle at center, ${glowColor} 0%, transparent 70%)`,
        }}
      />

      {/* Shimmer effect */}
      <motion.div
        className="absolute inset-0 opacity-0 group-hover:opacity-100"
        initial={{ x: '-100%' }}
        whileHover={{ x: '100%' }}
        transition={{ duration: 0.8, ease: 'easeInOut' }}
        style={{
          background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent)',
          width: '50%',
        }}
      />

      <div className="relative z-10">
        {/* Icon with animation */}
        {icon && (
          <motion.div
            className="mb-6 text-primary"
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ duration: 0.3 }}
          >
            {icon}
          </motion.div>
        )}

        {/* Content */}
        <h3 className="text-xl md:text-2xl font-bold mb-4 text-white group-hover:text-primary transition-colors duration-300">
          {title}
        </h3>
        <p className="text-white/70 mb-8 leading-relaxed group-hover:text-white/90 transition-colors duration-300">
          {description}
        </p>

        {/* Enhanced Button */}
        <motion.button
          className="relative px-6 py-3 bg-gradient-to-r from-primary/20 to-primary/10 border border-primary/30 text-white rounded-lg overflow-hidden group/btn"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <span className="relative z-10 font-medium">{buttonText}</span>

          {/* Button hover effect */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-primary/30 to-primary/20 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"
          />

          {/* Button shimmer */}
          <motion.div
            className="absolute inset-0 opacity-0 group-hover/btn:opacity-100"
            initial={{ x: '-100%' }}
            whileHover={{ x: '100%' }}
            transition={{ duration: 0.6, ease: 'easeInOut' }}
            style={{
              background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
              width: '50%',
            }}
          />
        </motion.button>
      </div>

      {/* Corner accent */}
      <div className="absolute top-0 right-0 w-20 h-20 opacity-20 group-hover:opacity-40 transition-opacity duration-300">
        <div
          className="w-full h-full"
          style={{
            background: `linear-gradient(135deg, ${glowColor} 0%, transparent 70%)`,
            clipPath: 'polygon(100% 0%, 0% 0%, 100% 100%)',
          }}
        />
      </div>
    </motion.div>
  );
}
